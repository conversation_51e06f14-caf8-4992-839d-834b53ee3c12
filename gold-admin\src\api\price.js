import request from '@/utils/request'

/**
 * 获取价格配置
 */
export function getPriceConfig() {
  return request({
    url: '/admin/price/config',
    method: 'get'
  })
}

/**
 * 更新价格配置
 * @param {string} type - 价格类型 (gold/platinum)
 * @param {object} data - 价格数据
 */
export function updatePriceConfig(type, data) {
  return request({
    url: `/admin/price/config/${type}`,
    method: 'put',
    data
  })
}

/**
 * 获取价格变更历史
 * @param {object} params - 查询参数
 */
export function getPriceHistory(params) {
  return request({
    url: '/admin/price/history',
    method: 'get',
    params
  })
}

/**
 * 获取实时金价
 */
export function getRealTimePrice() {
  return request({
    url: '/admin/price/realtime',
    method: 'get'
  })
}
