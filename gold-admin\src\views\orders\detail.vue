<template>
  <div class="app-container">
    <el-card v-loading="loading">
      <template #header>
        <div class="card-header">
          <span>订单详情</span>
          <el-button type="primary" @click="$router.go(-1)">返回</el-button>
        </div>
      </template>

      <div v-if="orderDetail" class="order-detail">
        <!-- 基本信息 -->
        <el-row :gutter="20">
          <el-col :span="12">
            <el-descriptions title="订单信息" :column="1" border>
              <el-descriptions-item label="订单号">
                {{ orderDetail.orderId }}
              </el-descriptions-item>
              <el-descriptions-item label="用户账号">
                {{ orderDetail.account }}
              </el-descriptions-item>
              <el-descriptions-item label="订单状态">
                <el-tag :type="getStatusType(orderDetail.status)">
                  {{ getStatusText(orderDetail.status) }}
                </el-tag>
              </el-descriptions-item>
              <el-descriptions-item label="创建时间">
                {{ formatTime(orderDetail.createTime) }}
              </el-descriptions-item>
              <el-descriptions-item label="更新时间">
                {{ formatTime(orderDetail.updateTime) }}
              </el-descriptions-item>
            </el-descriptions>
          </el-col>
          
          <el-col :span="12">
            <el-descriptions title="收货信息" :column="1" border v-loading="addressLoading">
              <el-descriptions-item label="收货人">
                {{ getReceiverName() }}
              </el-descriptions-item>
              <el-descriptions-item label="联系电话">
                {{ getReceiverPhone() }}
              </el-descriptions-item>
              <el-descriptions-item label="收货地址">
                {{ getFullAddress() }}
              </el-descriptions-item>
            </el-descriptions>
          </el-col>
        </el-row>

        <!-- 贵金属信息 -->
        <el-row :gutter="20" style="margin-top: 20px;">
          <el-col :span="12">
            <el-descriptions title="贵金属信息" :column="1" border>
              <el-descriptions-item label="贵金属类型">
                {{ getGoldTypeText(orderDetail.goldType) }}
              </el-descriptions-item>
              <el-descriptions-item label="贵金属状况">
                {{ orderDetail.goldCondition }}
              </el-descriptions-item>
              <el-descriptions-item label="成色">
                {{ orderDetail.purity }}
              </el-descriptions-item>
              <el-descriptions-item label="描述">
                {{ orderDetail.description }}
              </el-descriptions-item>
            </el-descriptions>
          </el-col>
          
          <el-col :span="12">
            <el-descriptions title="价格信息" :column="1" border>
              <el-descriptions-item label="预估重量">
                {{ orderDetail.estimatedWeight }}g
              </el-descriptions-item>
              <el-descriptions-item label="预估价格">
                ¥{{ formatPrice(orderDetail.estimatedPrice) }}
              </el-descriptions-item>
              <el-descriptions-item label="最终价格">
                <span v-if="orderDetail.finalPrice">¥{{ formatPrice(orderDetail.finalPrice) }}</span>
                <span v-else class="text-muted">待定</span>
              </el-descriptions-item>
            </el-descriptions>
          </el-col>
        </el-row>

        <!-- 检测结果 -->
        <div v-if="orderDetail.inspectionResult" style="margin-top: 20px;">
          <el-descriptions title="检测结果" :column="1" border>
            <el-descriptions-item label="检测结果">
              {{ orderDetail.inspectionResult }}
            </el-descriptions-item>
          </el-descriptions>
        </div>

        <!-- 快递信息 -->
        <div v-if="expressInfo && (expressInfo.expressCompany || expressInfo.trackingNumber)" style="margin-top: 20px;">
          <h3>快递信息</h3>
          <el-descriptions :column="2" border>
            <el-descriptions-item label="快递公司" v-if="expressInfo.expressCompany">
              {{ getExpressCompanyName(expressInfo.expressCompany) }}
            </el-descriptions-item>
            <el-descriptions-item label="快递单号" v-if="expressInfo.trackingNumber">
              {{ expressInfo.trackingNumber }}
            </el-descriptions-item>
            <el-descriptions-item label="取件时间" v-if="expressInfo.pickupTime" span="2">
              {{ formatTime(expressInfo.pickupTime) }}
            </el-descriptions-item>
            <el-descriptions-item label="备注" v-if="expressInfo.remark" span="2">
              {{ expressInfo.remark }}
            </el-descriptions-item>
          </el-descriptions>
        </div>

        <!-- 图片展示 -->
        <div v-if="orderDetail.imageList && orderDetail.imageList.length > 0" style="margin-top: 20px;">
          <h3>商品图片</h3>
          <div class="image-gallery">
            <el-image
              v-for="(image, index) in orderDetail.imageList"
              :key="index"
              :src="image"
              style="width: 200px; height: 200px; margin: 10px;"
              :preview-src-list="orderDetail.imageList"
              fit="cover"
            />
          </div>
        </div>
        <!-- 兼容旧的单图片字段 -->
        <div v-else-if="orderDetail.imageBase64" style="margin-top: 20px;">
          <h3>商品图片</h3>
          <el-image
            :src="orderDetail.imageBase64"
            style="width: 200px; height: 200px; margin: 10px;"
            :preview-src-list="[orderDetail.imageBase64]"
            fit="cover"
          />
        </div>

        <!-- 操作按钮 -->
        <div class="action-buttons" style="margin-top: 30px;">
          <el-button
            v-if="orderDetail.status === 1"
            type="success"
            @click="handleFillExpressInfo"
          >
            填写快递单号
          </el-button>
          <el-button
            v-if="orderDetail.status === 2"
            type="warning"
            @click="handleInspect"
          >
            检测
          </el-button>
          <el-button
            v-if="orderDetail.status === 3"
            type="primary"
            @click="handleUploadResult"
          >
            上传结果
          </el-button>
          <el-button
            v-if="orderDetail.status === 5"
            type="success"
            @click="handleComplete"
          >
            完成订单
          </el-button>
          <!-- 已送到店按钮 - 所有状态都显示 -->
          <el-button
            type="success"
            @click="handleDeliveredToStore"
          >
            已送到店
          </el-button>
        </div>
      </div>
    </el-card>

    <!-- 上传检测结果对话框 -->
    <el-dialog
      title="上传检测结果"
      v-model="processDialogVisible"
      width="500px"
    >
      <el-form
        ref="processFormRef"
        :model="processForm"
        label-width="100px"
      >
        <el-form-item label="实际重量(g)">
          <el-input-number
            v-model="processForm.actualWeight"
            :precision="2"
            :min="0"
            style="width: 100%"
          />
        </el-form-item>
        <el-form-item label="实际成色">
          <el-input v-model="processForm.actualPurity" />
        </el-form-item>
        <el-form-item label="最终价格">
          <el-input-number
            v-model="processForm.finalPrice"
            :precision="2"
            :min="0"
            style="width: 100%"
          />
        </el-form-item>
        <el-form-item label="检测结果">
          <el-input
            v-model="processForm.inspectionResult"
            type="textarea"
            :rows="3"
          />
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="processDialogVisible = false">
            取消
          </el-button>
          <el-button type="primary" @click="confirmProcess">
            上传结果
          </el-button>
        </div>
      </template>
    </el-dialog>

    <!-- 已送到店对话框 -->
    <el-dialog
      title="设置最终价格"
      v-model="deliveredDialogVisible"
      width="400px"
    >
      <el-form
        ref="deliveredFormRef"
        :model="deliveredForm"
        :rules="deliveredRules"
        label-width="100px"
      >
        <el-form-item label="订单号">
          <el-input v-model="deliveredForm.orderId" readonly />
        </el-form-item>
        <el-form-item label="最终价格" prop="finalPrice">
          <el-input-number
            v-model="deliveredForm.finalPrice"
            :precision="2"
            :min="0"
            style="width: 100%"
            placeholder="请输入最终价格"
          />
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="deliveredDialogVisible = false">
            取消
          </el-button>
          <el-button type="primary" @click="confirmDelivered" :loading="deliveredLoading">
            确认
          </el-button>
        </div>
      </template>
    </el-dialog>

    <!-- 填写快递信息对话框 -->
    <el-dialog
      title="填写快递信息"
      v-model="expressDialogVisible"
      width="500px"
    >
      <el-form
        ref="expressFormRef"
        :model="expressForm"
        :rules="expressRules"
        label-width="120px"
      >
        <el-form-item label="订单号">
          <el-input v-model="expressForm.orderId" readonly />
        </el-form-item>
        <el-form-item label="快递公司" prop="expressCompany">
          <el-select
            v-model="expressForm.expressCompany"
            placeholder="请选择快递公司"
            style="width: 100%"
          >
            <el-option label="顺丰速运" value="SF" />
            <el-option label="中通快递" value="ZTO" />
            <el-option label="圆通速递" value="YTO" />
            <el-option label="申通快递" value="STO" />
            <el-option label="韵达速递" value="YD" />
            <el-option label="百世快递" value="BEST" />
            <el-option label="德邦快递" value="DBL" />
            <el-option label="京东快递" value="JD" />
            <el-option label="邮政EMS" value="EMS" />
            <el-option label="天天快递" value="HHTT" />
          </el-select>
        </el-form-item>
        <el-form-item label="快递单号" prop="trackingNumber">
          <el-input
            v-model="expressForm.trackingNumber"
            placeholder="请输入快递单号"
          />
        </el-form-item>
        <el-form-item label="备注">
          <el-input
            v-model="expressForm.remark"
            type="textarea"
            :rows="3"
            placeholder="请输入备注信息（可选）"
          />
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="expressDialogVisible = false">
            取消
          </el-button>
          <el-button type="primary" @click="confirmExpressInfo" :loading="expressSubmitLoading">
            确认取件
          </el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue'
import { useRoute } from 'vue-router'
import { ElMessage, ElMessageBox } from 'element-plus'
import { getOrderDetail, updateOrderStatus, setFinalPrice, pickupOrder, getExpressInfo } from '@/api/orders'
import { getAddressDetail } from '@/api/address'
import { parseTime } from '@/utils'

const route = useRoute()

const loading = ref(true)
const orderDetail = ref(null)
const expressInfo = ref(null)
const addressDetail = ref(null)
const addressLoading = ref(false)
const processDialogVisible = ref(false)
const processForm = ref({
  actualWeight: 0,
  actualPurity: '',
  finalPrice: 0,
  inspectionResult: ''
})

// 已送到店对话框相关
const deliveredDialogVisible = ref(false)
const deliveredLoading = ref(false)
const deliveredForm = ref({
  orderId: '',
  finalPrice: null
})
const deliveredRules = {
  finalPrice: [
    { required: true, message: '请输入最终价格', trigger: 'blur' },
    { type: 'number', min: 0, message: '最终价格必须大于等于0', trigger: 'blur' }
  ]
}

// 快递信息对话框相关
const expressDialogVisible = ref(false)
const expressSubmitLoading = ref(false)
const expressForm = ref({
  orderId: '',
  expressCompany: '',
  trackingNumber: '',
  remark: ''
})
const expressRules = {
  expressCompany: [
    { required: true, message: '请选择快递公司', trigger: 'change' }
  ],
  trackingNumber: [
    { required: true, message: '请输入快递单号', trigger: 'blur' },
    { min: 8, max: 30, message: '快递单号长度应在8-30个字符之间', trigger: 'blur' }
  ]
}

async function fetchOrderDetail() {
  loading.value = true
  try {
    const response = await getOrderDetail(route.params.id)
    orderDetail.value = response.data
    console.log('获取订单详情成功:', orderDetail.value)

    // 如果订单状态大于1，尝试获取快递信息
    if (orderDetail.value && orderDetail.value.status > 1) {
      await fetchExpressInfo()
    }

    // 如果存在addressId，获取地址详情
    if (orderDetail.value && orderDetail.value.addressId) {
      await fetchAddressDetail()
    }
  } catch (error) {
    console.error('获取订单详情失败:', error)
    ElMessage.error(`获取订单详情失败: ${error.response?.data?.message || error.message || '请稍后重试'}`)

    // 开发环境下显示模拟数据
    if (process.env.NODE_ENV === 'development') {
      orderDetail.value = {
        id: 1,
        orderId: route.params.id,
        account: 'user001',
        goldType: 'jewelry', // 使用新的映射值
        goldCondition: '完好',
        purity: '足金',
        estimatedWeight: 10.5,
        estimatedPrice: 1500.00,
        finalPrice: null,
        status: 2, // 设置为已取件状态以便测试快递信息显示
        description: '黄金戒指，款式经典',
        inspectionResult: '',
        receiverName: '张三',
        receiverPhone: '***********',
        receiverAddress: '北京市朝阳区xxx街道xxx号',
        addressId: 'ADDR001', // 添加addressId用于测试
        imageList: ['https://via.placeholder.com/200x200', 'https://via.placeholder.com/200x200'],
        createTime: '2025-06-04T13:47:17',
        updateTime: '2025-06-04T13:47:17'
      }

      // 模拟快递信息
      if (orderDetail.value.status > 1) {
        expressInfo.value = {
          expressCompany: 'SF',
          trackingNumber: '***************',
          pickupTime: '2025-06-04T14:30:00',
          remark: '请注意包装，贵重物品'
        }
      }

      // 模拟地址详情
      if (orderDetail.value.addressId) {
        addressDetail.value = {
          receiverName: '张三',
          receiverPhone: '***********',
          province: '北京市',
          city: '朝阳区',
          district: '建国门街道',
          detailAddress: '建国门外大街1号国贸大厦A座1001室'
        }
      }
    }
  } finally {
    loading.value = false
  }
}

// 获取快递信息
async function fetchExpressInfo() {
  try {
    const response = await getExpressInfo(route.params.id)
    expressInfo.value = response.data
    console.log('获取快递信息成功:', expressInfo.value)
  } catch (error) {
    console.error('获取快递信息失败:', error)
    // 快递信息获取失败不显示错误提示，因为可能订单还没有快递信息
  }
}

// 获取地址详情
async function fetchAddressDetail() {
  if (!orderDetail.value?.addressId) return

  addressLoading.value = true
  try {
    const response = await getAddressDetail(orderDetail.value.addressId)
    addressDetail.value = response.data
    console.log('获取地址详情成功:', addressDetail.value)
  } catch (error) {
    console.error('获取地址详情失败:', error)
    // 地址详情获取失败不显示错误提示，使用订单中的默认地址信息
  } finally {
    addressLoading.value = false
  }
}

// 填写快递信息
function handleFillExpressInfo() {
  expressForm.value = {
    orderId: orderDetail.value.orderId,
    expressCompany: '',
    trackingNumber: '',
    remark: ''
  }
  expressDialogVisible.value = true
}

// 确认快递信息并取件
async function confirmExpressInfo() {
  // 验证表单
  if (!expressForm.value.expressCompany || !expressForm.value.trackingNumber) {
    ElMessage.error('请填写完整的快递信息')
    return
  }

  expressSubmitLoading.value = true
  try {
    // 调用取件API - 使用新的参数格式
    await pickupOrder(
      expressForm.value.orderId,
      expressForm.value.expressCompany,
      expressForm.value.trackingNumber
    )

    ElMessage.success('取件信息提交成功')
    expressDialogVisible.value = false
    fetchOrderDetail() // 刷新订单详情
  } catch (error) {
    console.error('取件操作失败:', error)
    ElMessage.error(`取件失败: ${error.response?.data?.message || error.message || '请稍后重试'}`)
  } finally {
    expressSubmitLoading.value = false
  }
}

// 检测操作 - 状态从2变为3
async function handleInspect() {
  try {
    await ElMessageBox.confirm('确定开始检测该订单吗？', '开始检测', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    })

    await updateOrderStatus(orderDetail.value.orderId, 3)
    ElMessage.success('已开始检测')
    fetchOrderDetail()
  } catch (error) {
    if (error !== 'cancel') {
      ElMessage.error('操作失败')
    }
  }
}

// 上传检测结果 - 状态从3变为4
function handleUploadResult() {
  processForm.value = {
    actualWeight: orderDetail.value.estimatedWeight,
    actualPurity: orderDetail.value.purity,
    finalPrice: orderDetail.value.estimatedPrice,
    inspectionResult: ''
  }
  processDialogVisible.value = true
}

async function confirmProcess() {
  try {
    await updateOrderStatus(orderDetail.value.orderId, 4)
    ElMessage.success('检测结果上传成功')
    processDialogVisible.value = false
    fetchOrderDetail()
  } catch (error) {
    ElMessage.error('上传失败')
  }
}

// 完成订单 - 状态从5变为6
async function handleComplete() {
  try {
    await ElMessageBox.confirm('确定要完成该订单吗？', '完成订单', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    })

    await updateOrderStatus(orderDetail.value.orderId, 6)
    ElMessage.success('订单已完成')
    fetchOrderDetail()
  } catch (error) {
    if (error !== 'cancel') {
      ElMessage.error('操作失败')
    }
  }
}

// 已送到店功能
function handleDeliveredToStore() {
  deliveredForm.value = {
    orderId: orderDetail.value.orderId,
    finalPrice: orderDetail.value.estimatedPrice || null
  }
  deliveredDialogVisible.value = true
}

async function confirmDelivered() {
  // 验证表单
  if (!deliveredForm.value.finalPrice || deliveredForm.value.finalPrice <= 0) {
    ElMessage.error('请输入有效的最终价格')
    return
  }

  deliveredLoading.value = true
  try {
    // 第一步：设置最终价格
    await setFinalPrice(orderDetail.value.orderId, deliveredForm.value.finalPrice)

    // 第二步：更新订单状态为已完成(6)
    await updateOrderStatus(orderDetail.value.orderId, 6)

    ElMessage.success('操作成功，订单已完成')
    deliveredDialogVisible.value = false
    fetchOrderDetail()
  } catch (error) {
    console.error('已送到店操作失败:', error)
    ElMessage.error(`操作失败: ${error.response?.data?.message || error.message || '请稍后重试'}`)
  } finally {
    deliveredLoading.value = false
  }
}

function getStatusType(status) {
  const statusMap = {
    0: 'danger',     // 已取消
    1: 'primary',    // 已下单
    2: 'warning',    // 已取件
    3: 'info',       // 待检测
    4: 'success',    // 已检测
    5: 'primary',    // 已确认
    6: 'success'     // 订单已完成
  }
  return statusMap[status] || 'info'
}

function getStatusText(status) {
  const statusMap = {
    0: '已取消',
    1: '已下单',
    2: '已取件',
    3: '待检测',
    4: '已检测',
    5: '已确认',
    6: '订单已完成'
  }
  return statusMap[status] || '未知'
}

function getGoldTypeText(goldType) {
  const typeMap = {
    'jewelry': '黄金',
    'bar': '铂金',
    'broken': '钯金',
    'other': '其他'
  }
  return typeMap[goldType] || goldType
}

function formatPrice(price) {
  if (price === null || price === undefined) return '0.00'
  return Number(price).toFixed(2)
}

function formatTime(time) {
  if (!time) return '-'
  return parseTime(time, '{y}-{m}-{d} {h}:{i}:{s}')
}

function getExpressCompanyName(code) {
  const companyMap = {
    'SF': '顺丰速运',
    'ZTO': '中通快递',
    'YTO': '圆通速递',
    'STO': '申通快递',
    'YD': '韵达速递',
    'BEST': '百世快递',
    'DBL': '德邦快递',
    'JD': '京东快递',
    'EMS': '邮政EMS',
    'HHTT': '天天快递'
  }
  return companyMap[code] || code
}

// 获取收货人姓名
function getReceiverName() {
  if (addressDetail.value?.receiverName) {
    return addressDetail.value.receiverName
  }
  return orderDetail.value?.receiverName || '-'
}

// 获取收货人电话
function getReceiverPhone() {
  if (addressDetail.value?.receiverPhone) {
    return addressDetail.value.receiverPhone
  }
  return orderDetail.value?.receiverPhone || '-'
}

// 获取完整地址
function getFullAddress() {
  if (addressDetail.value) {
    const { province, city, district, detailAddress } = addressDetail.value
    return `${province || ''}${city || ''}${district || ''}${detailAddress || ''}`
  }
  return orderDetail.value?.receiverAddress || '-'
}

onMounted(() => {
  fetchOrderDetail()
})
</script>

<style scoped>
.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.text-muted {
  color: #999;
}

.action-buttons {
  text-align: center;
}

.action-buttons .el-button {
  margin: 0 5px;
}

.image-gallery {
  display: flex;
  flex-wrap: wrap;
  gap: 10px;
}

.dialog-footer {
  text-align: right;
}
</style>
