<template>
  <div class="app-container">
    <div class="filter-container">
      <el-input
        v-model="listQuery.keyword"
        placeholder="请输入订单号或用户账号"
        style="width: 200px;"
        class="filter-item"
        @keyup.enter="handleFilter"
      />
      <el-select
        v-model="listQuery.status"
        placeholder="订单状态"
        clearable
        style="width: 120px"
        class="filter-item"
      >
        <el-option label="锁价中" value="1" />
        <el-option label="已确认" value="2" />
        <el-option label="已完成" value="3" />
        <el-option label="已过期" value="4" />
        <el-option label="已取消" value="5" />
      </el-select>
      <el-select
        v-model="listQuery.goldType"
        placeholder="黄金类型"
        clearable
        style="width: 120px"
        class="filter-item"
      >
        <el-option label="黄金首饰" value="jewelry" />
        <el-option label="金条" value="bar" />
        <el-option label="金币" value="coin" />
        <el-option label="其他" value="other" />
      </el-select>
      <el-button class="filter-item" type="primary" icon="Search" @click="handleFilter">
        搜索
      </el-button>
      <el-button class="filter-item" type="success" icon="Download" @click="handleDownload">
        导出
      </el-button>
    </div>

    <el-table
      :data="list"
      border
      fit
      highlight-current-row
      style="width: 100%;"
      v-loading="listLoading"
    >
      <el-table-column label="订单号" width="180" align="center">
        <template #default="scope">
          <span>{{ scope.row.orderNo }}</span>
        </template>
      </el-table-column>

      <el-table-column label="用户账号" width="120" align="center">
        <template #default="scope">
          <span>{{ scope.row.account }}</span>
        </template>
      </el-table-column>

      <el-table-column label="黄金类型" width="100" align="center">
        <template #default="scope">
          <span>{{ getGoldTypeText(scope.row.goldType) }}</span>
        </template>
      </el-table-column>

      <el-table-column label="锁价价格" width="120" align="right">
        <template #default="scope">
          ¥{{ scope.row.lockPrice }}/克
        </template>
      </el-table-column>

      <el-table-column label="预估重量(g)" width="120" align="right">
        <template #default="scope">
          {{ scope.row.estimatedWeight }}
        </template>
      </el-table-column>

      <el-table-column label="锁价金额" width="120" align="right">
        <template #default="scope">
          ¥{{ scope.row.lockAmount }}
        </template>
      </el-table-column>

      <el-table-column label="锁价时长" width="100" align="center">
        <template #default="scope">
          {{ scope.row.lockDuration }}小时
        </template>
      </el-table-column>

      <el-table-column label="剩余时间" width="120" align="center">
        <template #default="scope">
          <span v-if="scope.row.status === 1" :class="getRemainingTimeClass(scope.row.remainingTime)">
            {{ formatRemainingTime(scope.row.remainingTime) }}
          </span>
          <span v-else class="text-muted">-</span>
        </template>
      </el-table-column>

      <el-table-column label="状态" width="100" align="center">
        <template #default="scope">
          <el-tag :type="getStatusType(scope.row.status)">
            {{ getStatusText(scope.row.status) }}
          </el-tag>
        </template>
      </el-table-column>

      <el-table-column label="创建时间" width="160">
        <template #default="scope">
          {{ formatTime(scope.row.createTime) }}
        </template>
      </el-table-column>

      <el-table-column label="操作" align="center" width="200" class-name="small-padding fixed-width">
        <template #default="scope">
          <el-button type="primary" size="small" @click="handleDetail(scope.row)">
            详情
          </el-button>
          <el-button
            v-if="scope.row.status === 2"
            size="small"
            type="success"
            @click="handleComplete(scope.row)"
          >
            完成
          </el-button>
          <el-button
            v-if="scope.row.status === 1"
            size="small"
            type="danger"
            @click="handleCancel(scope.row)"
          >
            取消
          </el-button>
        </template>
      </el-table-column>
    </el-table>

    <pagination
      v-show="total > 0"
      :total="total"
      :page.sync="listQuery.page"
      :limit.sync="listQuery.limit"
      @pagination="getList"
    />

    <!-- 详情对话框 -->
    <el-dialog
      title="锁价订单详情"
      v-model="detailDialogVisible"
      width="800px"
    >
      <el-descriptions :column="2" border v-if="currentOrder">
        <el-descriptions-item label="订单号">{{ currentOrder.orderNo }}</el-descriptions-item>
        <el-descriptions-item label="用户账号">{{ currentOrder.account }}</el-descriptions-item>
        <el-descriptions-item label="黄金类型">{{ getGoldTypeText(currentOrder.goldType) }}</el-descriptions-item>
        <el-descriptions-item label="成色">{{ currentOrder.purity }}</el-descriptions-item>
        <el-descriptions-item label="锁价价格">¥{{ currentOrder.lockPrice }}/克</el-descriptions-item>
        <el-descriptions-item label="预估重量">{{ currentOrder.estimatedWeight }}克</el-descriptions-item>
        <el-descriptions-item label="锁价金额">¥{{ currentOrder.lockAmount }}</el-descriptions-item>
        <el-descriptions-item label="锁价时长">{{ currentOrder.lockDuration }}小时</el-descriptions-item>
        <el-descriptions-item label="状态">
          <el-tag :type="getStatusType(currentOrder.status)">
            {{ getStatusText(currentOrder.status) }}
          </el-tag>
        </el-descriptions-item>
        <el-descriptions-item label="剩余时间">
          <span v-if="currentOrder.status === 1" :class="getRemainingTimeClass(currentOrder.remainingTime)">
            {{ formatRemainingTime(currentOrder.remainingTime) }}
          </span>
          <span v-else>-</span>
        </el-descriptions-item>
        <el-descriptions-item label="创建时间" :span="2">{{ formatTime(currentOrder.createTime) }}</el-descriptions-item>
        <el-descriptions-item label="确认时间" :span="2">
          <span v-if="currentOrder.confirmTime">{{ formatTime(currentOrder.confirmTime) }}</span>
          <span v-else>-</span>
        </el-descriptions-item>
        <el-descriptions-item label="完成时间" :span="2">
          <span v-if="currentOrder.completeTime">{{ formatTime(currentOrder.completeTime) }}</span>
          <span v-else>-</span>
        </el-descriptions-item>
        <el-descriptions-item label="收货信息" :span="2">
          {{ currentOrder.receiverName }} / {{ currentOrder.receiverPhone }} / {{ currentOrder.receiverAddress }}
        </el-descriptions-item>
        <el-descriptions-item label="备注" :span="2">{{ currentOrder.remark || '-' }}</el-descriptions-item>
      </el-descriptions>
      
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="detailDialogVisible = false">关闭</el-button>
          <el-button
            v-if="currentOrder && currentOrder.status === 2"
            type="success"
            @click="handleComplete(currentOrder)"
          >
            完成订单
          </el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { getLockOrderList, updateLockOrderStatus, exportLockOrders } from '@/api/lock-orders'
import { parseTime } from '@/utils'
import Pagination from '@/components/Pagination/index.vue'

const list = ref([])
const total = ref(0)
const listLoading = ref(true)
const listQuery = ref({
  page: 1,
  limit: 20,
  keyword: '',
  status: '',
  goldType: ''
})

const detailDialogVisible = ref(false)
const currentOrder = ref(null)

async function getList() {
  listLoading.value = true
  try {
    const response = await getLockOrderList(listQuery.value)
    list.value = response.data.list
    total.value = response.data.total
  } catch (error) {
    console.error('获取锁价订单列表失败:', error)
    // 模拟数据
    list.value = [
      {
        id: 1,
        orderNo: 'LOCK20231201001',
        account: 'user001',
        goldType: 'jewelry',
        purity: '足金',
        lockPrice: 485.50,
        estimatedWeight: 10.5,
        lockAmount: 5097.75,
        lockDuration: 24,
        remainingTime: 18.5,
        status: 1,
        createTime: new Date().getTime(),
        confirmTime: null,
        completeTime: null,
        receiverName: '张三',
        receiverPhone: '***********',
        receiverAddress: '北京市朝阳区xxx',
        remark: '黄金戒指锁价'
      },
      {
        id: 2,
        orderNo: 'LOCK20231201002',
        account: 'user002',
        goldType: 'bar',
        purity: '999',
        lockPrice: 480.00,
        estimatedWeight: 50.0,
        lockAmount: 24000.00,
        lockDuration: 48,
        remainingTime: 0,
        status: 3,
        createTime: new Date().getTime() - *********,
        confirmTime: new Date().getTime() - ********,
        completeTime: new Date().getTime(),
        receiverName: '李四',
        receiverPhone: '***********',
        receiverAddress: '上海市浦东新区xxx',
        remark: '投资金条锁价'
      }
    ]
    total.value = 2
  } finally {
    listLoading.value = false
  }
}

function handleFilter() {
  listQuery.value.page = 1
  getList()
}

function handleDetail(row) {
  currentOrder.value = row
  detailDialogVisible.value = true
}

async function handleComplete(row) {
  try {
    await ElMessageBox.confirm('确定要完成该锁价订单吗？', '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    })

    await updateLockOrderStatus(row.id, { status: 3 })
    ElMessage.success('锁价订单已完成')
    detailDialogVisible.value = false
    getList()
  } catch (error) {
    if (error !== 'cancel') {
      ElMessage.error('操作失败')
    }
  }
}

async function handleCancel(row) {
  try {
    await ElMessageBox.confirm('确定要取消该锁价订单吗？', '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    })

    await updateLockOrderStatus(row.id, { status: 5 })
    ElMessage.success('锁价订单已取消')
    getList()
  } catch (error) {
    if (error !== 'cancel') {
      ElMessage.error('操作失败')
    }
  }
}

async function handleDownload() {
  try {
    const response = await exportLockOrders(listQuery.value)
    const blob = new Blob([response], { type: 'application/vnd.ms-excel' })
    const url = window.URL.createObjectURL(blob)
    const link = document.createElement('a')
    link.href = url
    link.download = `锁价订单数据_${new Date().getTime()}.xlsx`
    link.click()
    window.URL.revokeObjectURL(url)
  } catch (error) {
    ElMessage.error('导出失败')
  }
}

function getStatusType(status) {
  const statusMap = {
    1: 'primary',
    2: 'warning',
    3: 'success',
    4: 'info',
    5: 'danger'
  }
  return statusMap[status] || 'info'
}

function getStatusText(status) {
  const statusMap = {
    1: '锁价中',
    2: '已确认',
    3: '已完成',
    4: '已过期',
    5: '已取消'
  }
  return statusMap[status] || '未知'
}

function getGoldTypeText(type) {
  const typeMap = {
    jewelry: '黄金首饰',
    bar: '金条',
    coin: '金币',
    other: '其他'
  }
  return typeMap[type] || type
}

function formatRemainingTime(hours) {
  if (hours <= 0) return '已过期'
  if (hours < 1) return `${Math.floor(hours * 60)}分钟`
  return `${hours.toFixed(1)}小时`
}

function getRemainingTimeClass(hours) {
  if (hours <= 0) return 'text-danger'
  if (hours < 2) return 'text-warning'
  return 'text-success'
}

function formatTime(time) {
  return parseTime(time, '{y}-{m}-{d} {h}:{i}:{s}')
}

onMounted(() => {
  getList()
})
</script>

<style scoped>
.text-muted {
  color: #999;
}

.text-success {
  color: #67c23a;
}

.text-warning {
  color: #e6a23c;
}

.text-danger {
  color: #f56c6c;
}

.filter-container {
  padding-bottom: 10px;
}

.filter-item {
  margin-right: 10px;
}

.dialog-footer {
  text-align: right;
}
</style>
