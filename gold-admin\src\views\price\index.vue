<template>
  <div class="app-container">
    <div class="price-header">
      <h2>价格管理</h2>
      <p>设置黄金和铂金的买入卖出价格浮动</p>
    </div>

    <el-row :gutter="20">
      <!-- 黄金价格设置 -->
      <el-col :span="12">
        <el-card class="price-card">
          <template #header>
            <div class="card-header">
              <span>黄金价格设置</span>
              <el-icon class="gold-icon"><Coin /></el-icon>
            </div>
          </template>
          
          <el-form
            ref="goldFormRef"
            :model="goldForm"
            :rules="priceRules"
            label-width="120px"
            label-position="left"
          >
            <el-form-item label="当前金价" prop="currentPrice">
              <el-input-number
                v-model="goldForm.currentPrice"
                :precision="2"
                :min="0"
                :max="9999"
                style="width: 100%"
                placeholder="请输入当前金价"
              />
              <span class="unit">元/克</span>
            </el-form-item>
            
            <el-form-item label="买入价浮动" prop="buyFloat">
              <el-input-number
                v-model="goldForm.buyFloat"
                :precision="2"
                :min="-999"
                :max="999"
                style="width: 100%"
                placeholder="请输入买入价浮动"
              />
              <span class="unit">元/克</span>
            </el-form-item>
            
            <el-form-item label="卖出价浮动" prop="sellFloat">
              <el-input-number
                v-model="goldForm.sellFloat"
                :precision="2"
                :min="-999"
                :max="999"
                style="width: 100%"
                placeholder="请输入卖出价浮动"
              />
              <span class="unit">元/克</span>
            </el-form-item>
            
            <el-form-item label="实际买入价">
              <el-input
                :value="(goldForm.currentPrice + goldForm.buyFloat).toFixed(2)"
                readonly
                style="width: 100%"
              />
              <span class="unit">元/克</span>
            </el-form-item>
            
            <el-form-item label="实际卖出价">
              <el-input
                :value="(goldForm.currentPrice + goldForm.sellFloat).toFixed(2)"
                readonly
                style="width: 100%"
              />
              <span class="unit">元/克</span>
            </el-form-item>
            
            <el-form-item>
              <el-button type="primary" @click="saveGoldPrice" :loading="goldLoading">
                保存黄金价格
              </el-button>
              <el-button @click="resetGoldForm">重置</el-button>
            </el-form-item>
          </el-form>
        </el-card>
      </el-col>

      <!-- 铂金价格设置 -->
      <el-col :span="12">
        <el-card class="price-card">
          <template #header>
            <div class="card-header">
              <span>铂金价格设置</span>
              <el-icon class="platinum-icon"><Medal /></el-icon>
            </div>
          </template>
          
          <el-form
            ref="platinumFormRef"
            :model="platinumForm"
            :rules="priceRules"
            label-width="120px"
            label-position="left"
          >
            <el-form-item label="当前铂金价" prop="currentPrice">
              <el-input-number
                v-model="platinumForm.currentPrice"
                :precision="2"
                :min="0"
                :max="9999"
                style="width: 100%"
                placeholder="请输入当前铂金价"
              />
              <span class="unit">元/克</span>
            </el-form-item>
            
            <el-form-item label="买入价浮动" prop="buyFloat">
              <el-input-number
                v-model="platinumForm.buyFloat"
                :precision="2"
                :min="-999"
                :max="999"
                style="width: 100%"
                placeholder="请输入买入价浮动"
              />
              <span class="unit">元/克</span>
            </el-form-item>
            
            <el-form-item label="卖出价浮动" prop="sellFloat">
              <el-input-number
                v-model="platinumForm.sellFloat"
                :precision="2"
                :min="-999"
                :max="999"
                style="width: 100%"
                placeholder="请输入卖出价浮动"
              />
              <span class="unit">元/克</span>
            </el-form-item>
            
            <el-form-item label="实际买入价">
              <el-input
                :value="(platinumForm.currentPrice + platinumForm.buyFloat).toFixed(2)"
                readonly
                style="width: 100%"
              />
              <span class="unit">元/克</span>
            </el-form-item>
            
            <el-form-item label="实际卖出价">
              <el-input
                :value="(platinumForm.currentPrice + platinumForm.sellFloat).toFixed(2)"
                readonly
                style="width: 100%"
              />
              <span class="unit">元/克</span>
            </el-form-item>
            
            <el-form-item>
              <el-button type="primary" @click="savePlatinumPrice" :loading="platinumLoading">
                保存铂金价格
              </el-button>
              <el-button @click="resetPlatinumForm">重置</el-button>
            </el-form-item>
          </el-form>
        </el-card>
      </el-col>
    </el-row>

    <!-- 价格历史记录 -->
    <el-card class="history-card" style="margin-top: 20px;">
      <template #header>
        <div class="card-header">
          <span>价格变更历史</span>
          <el-button type="primary" size="small" @click="refreshHistory">
            刷新
          </el-button>
        </div>
      </template>
      
      <el-table :data="priceHistory" border style="width: 100%">
        <el-table-column prop="type" label="类型" width="100" align="center">
          <template #default="scope">
            <el-tag :type="scope.row.type === 'gold' ? 'warning' : 'success'">
              {{ scope.row.type === 'gold' ? '黄金' : '铂金' }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="currentPrice" label="当前价格" width="120" align="right">
          <template #default="scope">
            ¥{{ scope.row.currentPrice }}/克
          </template>
        </el-table-column>
        <el-table-column prop="buyFloat" label="买入浮动" width="120" align="right">
          <template #default="scope">
            <span :class="scope.row.buyFloat >= 0 ? 'text-success' : 'text-danger'">
              {{ scope.row.buyFloat >= 0 ? '+' : '' }}{{ scope.row.buyFloat }}
            </span>
          </template>
        </el-table-column>
        <el-table-column prop="sellFloat" label="卖出浮动" width="120" align="right">
          <template #default="scope">
            <span :class="scope.row.sellFloat >= 0 ? 'text-success' : 'text-danger'">
              {{ scope.row.sellFloat >= 0 ? '+' : '' }}{{ scope.row.sellFloat }}
            </span>
          </template>
        </el-table-column>
        <el-table-column prop="operator" label="操作人" width="120" />
        <el-table-column prop="updateTime" label="更新时间" width="180">
          <template #default="scope">
            {{ formatTime(scope.row.updateTime) }}
          </template>
        </el-table-column>
        <el-table-column prop="remark" label="备注" />
      </el-table>
    </el-card>
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue'
import { ElMessage } from 'element-plus'
import { Coin, Medal } from '@element-plus/icons-vue'
import { getPriceConfig, updatePriceConfig, getPriceHistory } from '@/api/price'
import { parseTime } from '@/utils'

const goldFormRef = ref()
const platinumFormRef = ref()
const goldLoading = ref(false)
const platinumLoading = ref(false)

const goldForm = ref({
  currentPrice: 0,
  buyFloat: 0,
  sellFloat: 0
})

const platinumForm = ref({
  currentPrice: 0,
  buyFloat: 0,
  sellFloat: 0
})

const priceHistory = ref([])

const priceRules = {
  currentPrice: [
    { required: true, message: '请输入当前价格', trigger: 'blur' },
    { type: 'number', min: 0, message: '价格不能小于0', trigger: 'blur' }
  ],
  buyFloat: [
    { required: true, message: '请输入买入价浮动', trigger: 'blur' },
    { type: 'number', message: '请输入有效数字', trigger: 'blur' }
  ],
  sellFloat: [
    { required: true, message: '请输入卖出价浮动', trigger: 'blur' },
    { type: 'number', message: '请输入有效数字', trigger: 'blur' }
  ]
}

async function loadPriceConfig() {
  try {
    const response = await getPriceConfig()
    goldForm.value = response.data.gold || goldForm.value
    platinumForm.value = response.data.platinum || platinumForm.value
  } catch (error) {
    console.error('加载价格配置失败:', error)
    // 模拟数据
    goldForm.value = {
      currentPrice: 485.50,
      buyFloat: -15.00,
      sellFloat: -25.00
    }
    platinumForm.value = {
      currentPrice: 215.80,
      buyFloat: -8.00,
      sellFloat: -12.00
    }
  }
}

async function saveGoldPrice() {
  if (!goldFormRef.value) return
  
  try {
    await goldFormRef.value.validate()
    goldLoading.value = true
    
    await updatePriceConfig('gold', goldForm.value)
    ElMessage.success('黄金价格保存成功')
    refreshHistory()
  } catch (error) {
    if (error !== false) {
      ElMessage.error('保存失败')
    }
  } finally {
    goldLoading.value = false
  }
}

async function savePlatinumPrice() {
  if (!platinumFormRef.value) return
  
  try {
    await platinumFormRef.value.validate()
    platinumLoading.value = true
    
    await updatePriceConfig('platinum', platinumForm.value)
    ElMessage.success('铂金价格保存成功')
    refreshHistory()
  } catch (error) {
    if (error !== false) {
      ElMessage.error('保存失败')
    }
  } finally {
    platinumLoading.value = false
  }
}

function resetGoldForm() {
  goldFormRef.value?.resetFields()
}

function resetPlatinumForm() {
  platinumFormRef.value?.resetFields()
}

async function refreshHistory() {
  try {
    const response = await getPriceHistory()
    priceHistory.value = response.data.list || []
  } catch (error) {
    console.error('加载价格历史失败:', error)
    // 模拟数据
    priceHistory.value = [
      {
        id: 1,
        type: 'gold',
        currentPrice: 485.50,
        buyFloat: -15.00,
        sellFloat: -25.00,
        operator: 'admin',
        updateTime: new Date().getTime(),
        remark: '调整黄金价格浮动'
      },
      {
        id: 2,
        type: 'platinum',
        currentPrice: 215.80,
        buyFloat: -8.00,
        sellFloat: -12.00,
        operator: 'admin',
        updateTime: new Date().getTime() - 86400000,
        remark: '调整铂金价格浮动'
      }
    ]
  }
}

function formatTime(time) {
  return parseTime(time, '{y}-{m}-{d} {h}:{i}:{s}')
}

onMounted(() => {
  loadPriceConfig()
  refreshHistory()
})
</script>

<style scoped>
.price-header {
  margin-bottom: 20px;
}

.price-header h2 {
  margin: 0 0 8px 0;
  color: #303133;
}

.price-header p {
  margin: 0;
  color: #909399;
}

.price-card {
  height: 100%;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.gold-icon {
  color: #f39c12;
  font-size: 20px;
}

.platinum-icon {
  color: #95a5a6;
  font-size: 20px;
}

.unit {
  margin-left: 8px;
  color: #909399;
  font-size: 12px;
}

.text-success {
  color: #67c23a;
}

.text-danger {
  color: #f56c6c;
}

.history-card {
  margin-top: 20px;
}
</style>
