<template>
  <div class="app-container">
    <!-- 搜索筛选 -->
    <div class="filter-container">
      <el-input
        v-model="listQuery.keyword"
        placeholder="请输入用户名或账号"
        style="width: 200px;"
        class="filter-item"
        @keyup.enter="handleFilter"
      />
      <el-button
        class="filter-item"
        type="primary"
        icon="Search"
        @click="handleFilter"
      >
        搜索
      </el-button>
    </div>

    <!-- 表格 -->
    <el-table
      v-loading="listLoading"
      :data="list"
      element-loading-text="加载中"
      border
      fit
      highlight-current-row
    >
      <el-table-column align="center" label="ID" width="80">
        <template #default="scope">
          {{ scope.row.id }}
        </template>
      </el-table-column>

      <el-table-column label="用户名" width="180">
        <template #default="scope">
          {{ scope.row.username }}
        </template>
      </el-table-column>

      <el-table-column label="账号" width="auto">
        <template #default="scope">
          {{ scope.row.account }}
        </template>
      </el-table-column>

      <el-table-column label="用户等级" width="250" align="center">
        <template #default="scope">
          <el-tag :type="getUserLevelType(scope.row.userLevel)">
            {{ getUserLevelText(scope.row.userLevel) }}
          </el-tag>
        </template>
      </el-table-column>

      <el-table-column label="创建时间" width="260">
        <template #default="scope">
          {{ formatTime(scope.row.createTime) }}
        </template>
      </el-table-column>

      <el-table-column label="操作" align="center" width="280">
        <template #default="scope">
          <el-button type="primary" size="small" @click="handleUpdateLevel(scope.row)">
            修改等级
          </el-button>
          <el-button type="success" size="small" @click="handleViewRelation(scope.row)">
            查看关系
          </el-button>
        </template>
      </el-table-column>
    </el-table>

    <!-- 分页 -->
    <pagination
      v-show="total > 0"
      :total="total"
      :page.sync="listQuery.page"
      :limit.sync="listQuery.limit"
      @pagination="getList"
    />

    <!-- 修改用户等级对话框 -->
    <el-dialog
      title="修改用户等级"
      v-model="levelDialogVisible"
      width="400px"
    >
      <el-form
        ref="levelFormRef"
        :model="levelForm"
        label-width="80px"
      >
        <el-form-item label="用户等级" prop="userLevel">
          <el-select v-model="levelForm.userLevel" placeholder="请选择">
            <el-option label="VIP2" :value="2" />
            <el-option label="VIP3" :value="3" />
          </el-select>
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="levelDialogVisible = false">取消</el-button>
          <el-button type="primary" @click="handleUpdateUserLevel">确认</el-button>
        </div>
      </template>
    </el-dialog>

    <!-- 查看用户关系对话框 -->
    <el-dialog
      :title="relationData.user ? `用户关系 - ${relationData.user.username}（${relationData.user.account}）` : '用户关系'"
      v-model="relationDialogVisible"
      width="600px"
    >
      <el-tabs v-model="activeTab">
        <el-tab-pane label="下级" name="children">
          <el-table :data="relationData.children" border style="width: 100%">
            <el-table-column prop="username" label="用户名" width="120" />
            <el-table-column prop="account" label="账号" width="120" />
            <el-table-column label="用户等级" width="100" align="center">
              <template #default="scope">
                <el-tag :type="getUserLevelType(scope.row.userLevel)">
                  {{ getUserLevelText(scope.row.userLevel) }}
                </el-tag>
              </template>
            </el-table-column>
            <el-table-column prop="createTime" label="创建时间" width="160">
              <template #default="scope">
                {{ formatTime(scope.row.createTime) }}
              </template>
            </el-table-column>
          </el-table>
        </el-tab-pane>
        <el-tab-pane label="下下级" name="subChildren">
          <el-table :data="relationData.subChildren" border style="width: 100%">
            <el-table-column prop="username" label="用户名" width="120" />
            <el-table-column prop="account" label="账号" width="120" />
            <el-table-column label="用户等级" width="100" align="center">
              <template #default="scope">
                <el-tag :type="getUserLevelType(scope.row.userLevel)">
                  {{ getUserLevelText(scope.row.userLevel) }}
                </el-tag>
              </template>
            </el-table-column>
            <el-table-column prop="createTime" label="创建时间" width="160">
              <template #default="scope">
                {{ formatTime(scope.row.createTime) }}
              </template>
            </el-table-column>
          </el-table>
        </el-tab-pane>
      </el-tabs>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue'
import { ElMessage } from 'element-plus'
import { getUserList, updateUserLevel, getUserRelationTree } from '@/api/users'
import { parseTime } from '@/utils'
import Pagination from '@/components/Pagination/index.vue'

const list = ref([])
const total = ref(0)
const listLoading = ref(true)
const listQuery = ref({
  page: 1,
  limit: 20,
  keyword: ''
})

// 修改用户等级相关
const levelDialogVisible = ref(false)
const levelFormRef = ref()
const levelForm = ref({
  account: '',
  userLevel: 2
})

// 查看用户关系相关
const relationDialogVisible = ref(false)
const activeTab = ref('children')
const relationData = ref({
  user: null,
  children: [],
  subChildren: []
})

async function getList() {
  listLoading.value = true
  try {
    const response = await getUserList(listQuery.value)
    if (response.code === 200) {
      list.value = response.data
      total.value = response.data.length
    } else {
      ElMessage.error(response.message || '获取用户列表失败')
    }
  } catch (error) {
    console.error('获取用户列表失败:', error)
    ElMessage.error('获取用户列表失败')
  } finally {
    listLoading.value = false
  }
}

function handleFilter() {
  listQuery.value.page = 1
  getList()
}

// 修改用户等级
function handleUpdateLevel(row) {
  levelForm.value = {
    account: row.account,
    userLevel: row.userLevel
  }
  levelDialogVisible.value = true
}

async function handleUpdateUserLevel() {
  try {
    const response = await updateUserLevel(levelForm.value.account, levelForm.value.userLevel)
    if (response.code === 200) {
      ElMessage.success('修改成功')
      levelDialogVisible.value = false
      getList()
    } else {
      ElMessage.error(response.message || '修改失败')
    }
  } catch (error) {
    console.error('修改用户等级失败:', error)
    ElMessage.error('修改用户等级失败')
  }
}

// 查看用户关系
async function handleViewRelation(row) {
  try {
    const response = await getUserRelationTree(row.account)
    if (response.code === 200) {
      relationData.value = response.data
      relationDialogVisible.value = true
    } else {
      ElMessage.error(response.message || '获取用户关系失败')
    }
  } catch (error) {
    console.error('获取用户关系失败:', error)
    ElMessage.error('获取用户关系失败')
  }
}

function formatTime(time) {
  if (!time) return ''
  return parseTime(time, '{y}-{m}-{d} {h}:{i}:{s}')
}

function getUserLevelType(level) {
  const types = {
    0: 'info',
    1: 'success',
    2: 'warning',
    3: 'danger'
  }
  return types[level] || 'info'
}

function getUserLevelText(level) {
  const texts = {
    0: '普通用户',
    1: 'VIP1',
    2: 'VIP2',
    3: 'VIP3'
  }
  return texts[level] || '普通用户'
}

onMounted(() => {
  getList()
})
</script>

<style lang="scss" scoped>
.filter-container {
  padding-bottom: 10px;
  .filter-item {
    margin-right: 10px;
  }
}

.dialog-footer {
  text-align: right;
}
</style>
