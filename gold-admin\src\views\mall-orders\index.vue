<template>
  <div class="app-container">
    <div class="filter-container">
      <el-input
        v-model="listQuery.keyword"
        placeholder="请输入订单号或用户账号"
        style="width: 200px;"
        class="filter-item"
        @keyup.enter="handleFilter"
      />
      <el-select
        v-model="listQuery.status"
        placeholder="订单状态"
        clearable
        style="width: 120px"
        class="filter-item"
      >
        <el-option label="待付款" value="1" />
        <el-option label="待发货" value="2" />
        <el-option label="待收货" value="3" />
        <el-option label="已完成" value="4" />
        <el-option label="已取消" value="5" />
        <el-option label="退款中" value="6" />
        <el-option label="已退款" value="7" />
      </el-select>
      <el-select
        v-model="listQuery.category"
        placeholder="商品分类"
        clearable
        style="width: 120px"
        class="filter-item"
      >
        <el-option label="黄金首饰" value="jewelry" />
        <el-option label="金条" value="bar" />
        <el-option label="金币" value="coin" />
        <el-option label="其他" value="other" />
      </el-select>
      <el-button class="filter-item" type="primary" icon="Search" @click="handleFilter">
        搜索
      </el-button>
      <el-button class="filter-item" type="success" icon="Download" @click="handleDownload">
        导出
      </el-button>
    </div>

    <el-table
      :data="list"
      border
      fit
      highlight-current-row
      style="width: 100%;"
      v-loading="listLoading"
    >
      <el-table-column label="订单号" width="180" align="center">
        <template #default="scope">
          <span>{{ scope.row.orderNo }}</span>
        </template>
      </el-table-column>

      <el-table-column label="用户账号" width="120" align="center">
        <template #default="scope">
          <span>{{ scope.row.account }}</span>
        </template>
      </el-table-column>

      <el-table-column label="商品信息" width="200">
        <template #default="scope">
          <div class="product-info">
            <img :src="scope.row.productImage" alt="" class="product-image" />
            <div class="product-details">
              <div class="product-name">{{ scope.row.productName }}</div>
              <div class="product-spec">{{ scope.row.productSpec }}</div>
            </div>
          </div>
        </template>
      </el-table-column>

      <el-table-column label="数量" width="80" align="center">
        <template #default="scope">
          {{ scope.row.quantity }}
        </template>
      </el-table-column>

      <el-table-column label="单价" width="100" align="right">
        <template #default="scope">
          ¥{{ scope.row.unitPrice }}
        </template>
      </el-table-column>

      <el-table-column label="总金额" width="120" align="right">
        <template #default="scope">
          ¥{{ scope.row.totalAmount }}
        </template>
      </el-table-column>

      <el-table-column label="支付方式" width="100" align="center">
        <template #default="scope">
          {{ getPaymentMethodText(scope.row.paymentMethod) }}
        </template>
      </el-table-column>

      <el-table-column label="状态" width="100" align="center">
        <template #default="scope">
          <el-tag :type="getStatusType(scope.row.status)">
            {{ getStatusText(scope.row.status) }}
          </el-tag>
        </template>
      </el-table-column>

      <el-table-column label="创建时间" width="160">
        <template #default="scope">
          {{ formatTime(scope.row.createTime) }}
        </template>
      </el-table-column>

      <el-table-column label="操作" align="center" width="200" class-name="small-padding fixed-width">
        <template #default="scope">
          <el-button type="primary" size="small" @click="handleDetail(scope.row)">
            详情
          </el-button>
          <el-button
            v-if="scope.row.status === 2"
            size="small"
            type="success"
            @click="handleShip(scope.row)"
          >
            发货
          </el-button>
          <el-button
            v-if="scope.row.status === 6"
            size="small"
            type="warning"
            @click="handleRefund(scope.row)"
          >
            退款
          </el-button>
        </template>
      </el-table-column>
    </el-table>

    <pagination
      v-show="total > 0"
      :total="total"
      :page.sync="listQuery.page"
      :limit.sync="listQuery.limit"
      @pagination="getList"
    />

    <!-- 详情对话框 -->
    <el-dialog
      title="商城订单详情"
      v-model="detailDialogVisible"
      width="800px"
    >
      <el-descriptions :column="2" border v-if="currentOrder">
        <el-descriptions-item label="订单号">{{ currentOrder.orderNo }}</el-descriptions-item>
        <el-descriptions-item label="用户账号">{{ currentOrder.account }}</el-descriptions-item>
        <el-descriptions-item label="商品名称">{{ currentOrder.productName }}</el-descriptions-item>
        <el-descriptions-item label="商品规格">{{ currentOrder.productSpec }}</el-descriptions-item>
        <el-descriptions-item label="数量">{{ currentOrder.quantity }}</el-descriptions-item>
        <el-descriptions-item label="单价">¥{{ currentOrder.unitPrice }}</el-descriptions-item>
        <el-descriptions-item label="总金额">¥{{ currentOrder.totalAmount }}</el-descriptions-item>
        <el-descriptions-item label="支付方式">{{ getPaymentMethodText(currentOrder.paymentMethod) }}</el-descriptions-item>
        <el-descriptions-item label="状态">
          <el-tag :type="getStatusType(currentOrder.status)">
            {{ getStatusText(currentOrder.status) }}
          </el-tag>
        </el-descriptions-item>
        <el-descriptions-item label="快递公司">{{ currentOrder.expressCompany || '-' }}</el-descriptions-item>
        <el-descriptions-item label="快递单号">{{ currentOrder.trackingNumber || '-' }}</el-descriptions-item>
        <el-descriptions-item label="创建时间" :span="2">{{ formatTime(currentOrder.createTime) }}</el-descriptions-item>
        <el-descriptions-item label="付款时间" :span="2">
          <span v-if="currentOrder.payTime">{{ formatTime(currentOrder.payTime) }}</span>
          <span v-else>-</span>
        </el-descriptions-item>
        <el-descriptions-item label="发货时间" :span="2">
          <span v-if="currentOrder.shipTime">{{ formatTime(currentOrder.shipTime) }}</span>
          <span v-else>-</span>
        </el-descriptions-item>
        <el-descriptions-item label="收货信息" :span="2">
          {{ currentOrder.receiverName }} / {{ currentOrder.receiverPhone }} / {{ currentOrder.receiverAddress }}
        </el-descriptions-item>
        <el-descriptions-item label="备注" :span="2">{{ currentOrder.remark || '-' }}</el-descriptions-item>
      </el-descriptions>
    </el-dialog>

    <!-- 发货对话框 -->
    <el-dialog
      title="订单发货"
      v-model="shipDialogVisible"
      width="500px"
    >
      <el-form
        ref="shipFormRef"
        :model="shipForm"
        :rules="shipRules"
        label-width="100px"
      >
        <el-form-item label="快递公司" prop="expressCompany">
          <el-select v-model="shipForm.expressCompany" style="width: 100%">
            <el-option label="顺丰速运" value="SF" />
            <el-option label="圆通速递" value="YTO" />
            <el-option label="中通快递" value="ZTO" />
            <el-option label="申通快递" value="STO" />
            <el-option label="韵达速递" value="YD" />
            <el-option label="邮政EMS" value="EMS" />
          </el-select>
        </el-form-item>
        <el-form-item label="快递单号" prop="trackingNumber">
          <el-input v-model="shipForm.trackingNumber" placeholder="请输入快递单号" />
        </el-form-item>
        <el-form-item label="发货备注">
          <el-input
            v-model="shipForm.remark"
            type="textarea"
            :rows="3"
            placeholder="请输入发货备注"
          />
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="shipDialogVisible = false">取消</el-button>
          <el-button type="primary" @click="confirmShip">确认发货</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { getMallOrderList, updateMallOrderStatus, shipMallOrder, refundMallOrder, exportMallOrders } from '@/api/mall-orders'
import { parseTime } from '@/utils'
import Pagination from '@/components/Pagination/index.vue'

const list = ref([])
const total = ref(0)
const listLoading = ref(true)
const listQuery = ref({
  page: 1,
  limit: 20,
  keyword: '',
  status: '',
  category: ''
})

const detailDialogVisible = ref(false)
const shipDialogVisible = ref(false)
const currentOrder = ref(null)
const shipFormRef = ref()

const shipForm = ref({
  expressCompany: '',
  trackingNumber: '',
  remark: ''
})

const shipRules = {
  expressCompany: [
    { required: true, message: '请选择快递公司', trigger: 'change' }
  ],
  trackingNumber: [
    { required: true, message: '请输入快递单号', trigger: 'blur' }
  ]
}

async function getList() {
  listLoading.value = true
  try {
    const response = await getMallOrderList(listQuery.value)
    list.value = response.data.list
    total.value = response.data.total
  } catch (error) {
    console.error('获取商城订单列表失败:', error)
    // 模拟数据
    list.value = [
      {
        id: 1,
        orderNo: 'MALL20231201001',
        account: 'user001',
        productName: '黄金戒指',
        productSpec: '18K金 6号',
        productImage: '/images/ring.jpg',
        quantity: 1,
        unitPrice: 2580.00,
        totalAmount: 2580.00,
        paymentMethod: 'alipay',
        status: 2,
        expressCompany: '',
        trackingNumber: '',
        createTime: new Date().getTime(),
        payTime: new Date().getTime(),
        shipTime: null,
        receiverName: '张三',
        receiverPhone: '***********',
        receiverAddress: '北京市朝阳区xxx',
        remark: ''
      },
      {
        id: 2,
        orderNo: 'MALL20231201002',
        account: 'user002',
        productName: '投资金条',
        productSpec: '999纯金 10克',
        productImage: '/images/bar.jpg',
        quantity: 2,
        unitPrice: 4850.00,
        totalAmount: 9700.00,
        paymentMethod: 'wechat',
        status: 4,
        expressCompany: 'SF',
        trackingNumber: 'SF1234567890',
        createTime: new Date().getTime() - *********,
        payTime: new Date().getTime() - *********,
        shipTime: new Date().getTime() - ********,
        receiverName: '李四',
        receiverPhone: '***********',
        receiverAddress: '上海市浦东新区xxx',
        remark: '投资收藏'
      }
    ]
    total.value = 2
  } finally {
    listLoading.value = false
  }
}

function handleFilter() {
  listQuery.value.page = 1
  getList()
}

function handleDetail(row) {
  currentOrder.value = row
  detailDialogVisible.value = true
}

function handleShip(row) {
  currentOrder.value = row
  shipForm.value = {
    expressCompany: '',
    trackingNumber: '',
    remark: ''
  }
  shipDialogVisible.value = true
}

async function confirmShip() {
  if (!shipFormRef.value) return
  
  try {
    await shipFormRef.value.validate()
    
    await shipMallOrder(currentOrder.value.id, shipForm.value)
    ElMessage.success('订单发货成功')
    shipDialogVisible.value = false
    getList()
  } catch (error) {
    if (error !== false) {
      ElMessage.error('发货失败')
    }
  }
}

async function handleRefund(row) {
  try {
    await ElMessageBox.confirm('确定要退款该订单吗？', '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    })

    await refundMallOrder(row.id)
    ElMessage.success('订单退款成功')
    getList()
  } catch (error) {
    if (error !== 'cancel') {
      ElMessage.error('退款失败')
    }
  }
}

async function handleDownload() {
  try {
    const response = await exportMallOrders(listQuery.value)
    const blob = new Blob([response], { type: 'application/vnd.ms-excel' })
    const url = window.URL.createObjectURL(blob)
    const link = document.createElement('a')
    link.href = url
    link.download = `商城订单数据_${new Date().getTime()}.xlsx`
    link.click()
    window.URL.revokeObjectURL(url)
  } catch (error) {
    ElMessage.error('导出失败')
  }
}

function getStatusType(status) {
  const statusMap = {
    1: 'warning',
    2: 'primary',
    3: 'info',
    4: 'success',
    5: 'danger',
    6: 'warning',
    7: 'info'
  }
  return statusMap[status] || 'info'
}

function getStatusText(status) {
  const statusMap = {
    1: '待付款',
    2: '待发货',
    3: '待收货',
    4: '已完成',
    5: '已取消',
    6: '退款中',
    7: '已退款'
  }
  return statusMap[status] || '未知'
}

function getPaymentMethodText(method) {
  const methodMap = {
    alipay: '支付宝',
    wechat: '微信支付',
    balance: '余额支付',
    bank: '银行卡'
  }
  return methodMap[method] || method
}

function formatTime(time) {
  return parseTime(time, '{y}-{m}-{d} {h}:{i}:{s}')
}

onMounted(() => {
  getList()
})
</script>

<style scoped>
.filter-container {
  padding-bottom: 10px;
}

.filter-item {
  margin-right: 10px;
}

.product-info {
  display: flex;
  align-items: center;
}

.product-image {
  width: 50px;
  height: 50px;
  object-fit: cover;
  border-radius: 4px;
  margin-right: 10px;
}

.product-details {
  flex: 1;
}

.product-name {
  font-weight: bold;
  margin-bottom: 4px;
}

.product-spec {
  font-size: 12px;
  color: #999;
}

.dialog-footer {
  text-align: right;
}
</style>
