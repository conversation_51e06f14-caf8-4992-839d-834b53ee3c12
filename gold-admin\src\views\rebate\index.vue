<template>
  <div class="app-container">
    <div class="rebate-header">
      <h2>返点管理</h2>
      <p>设置用户推荐返点规则和查看返点记录</p>
    </div>

    <!-- 返点规则设置 -->
    <el-card class="rule-card">
      <template #header>
        <div class="card-header">
          <span>返点规则设置</span>
          <el-button type="primary" size="small" @click="saveRules" :loading="ruleLoading">
            保存规则
          </el-button>
        </div>
      </template>
      
      <el-form
        ref="ruleFormRef"
        :model="ruleForm"
        :rules="ruleRules"
        label-width="150px"
      >
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="一级推荐返点率" prop="level1Rate">
              <el-input-number
                v-model="ruleForm.level1Rate"
                :precision="2"
                :min="0"
                :max="100"
                style="width: 100%"
              />
              <span class="unit">%</span>
            </el-form-item>
            
            <el-form-item label="二级推荐返点率" prop="level2Rate">
              <el-input-number
                v-model="ruleForm.level2Rate"
                :precision="2"
                :min="0"
                :max="100"
                style="width: 100%"
              />
              <span class="unit">%</span>
            </el-form-item>
            
            <el-form-item label="三级推荐返点率" prop="level3Rate">
              <el-input-number
                v-model="ruleForm.level3Rate"
                :precision="2"
                :min="0"
                :max="100"
                style="width: 100%"
              />
              <span class="unit">%</span>
            </el-form-item>
          </el-col>
          
          <el-col :span="12">
            <el-form-item label="最低返点金额" prop="minAmount">
              <el-input-number
                v-model="ruleForm.minAmount"
                :precision="2"
                :min="0"
                style="width: 100%"
              />
              <span class="unit">元</span>
            </el-form-item>
            
            <el-form-item label="返点结算周期" prop="settlementCycle">
              <el-select v-model="ruleForm.settlementCycle" style="width: 100%">
                <el-option label="实时结算" value="realtime" />
                <el-option label="每日结算" value="daily" />
                <el-option label="每周结算" value="weekly" />
                <el-option label="每月结算" value="monthly" />
              </el-select>
            </el-form-item>
            
            <el-form-item label="返点状态" prop="status">
              <el-switch
                v-model="ruleForm.status"
                active-text="启用"
                inactive-text="禁用"
              />
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
    </el-card>

    <!-- 返点统计 -->
    <el-card class="stats-card" style="margin-top: 20px;">
      <template #header>
        <div class="card-header">
          <span>返点统计</span>
          <el-button type="primary" size="small" @click="refreshStats">
            刷新
          </el-button>
        </div>
      </template>
      
      <el-row :gutter="20">
        <el-col :span="6">
          <div class="stat-item">
            <div class="stat-value">¥{{ rebateStats.todayRebate }}</div>
            <div class="stat-label">今日返点</div>
          </div>
        </el-col>
        <el-col :span="6">
          <div class="stat-item">
            <div class="stat-value">¥{{ rebateStats.monthRebate }}</div>
            <div class="stat-label">本月返点</div>
          </div>
        </el-col>
        <el-col :span="6">
          <div class="stat-item">
            <div class="stat-value">¥{{ rebateStats.totalRebate }}</div>
            <div class="stat-label">累计返点</div>
          </div>
        </el-col>
        <el-col :span="6">
          <div class="stat-item">
            <div class="stat-value">{{ rebateStats.rebateUsers }}</div>
            <div class="stat-label">返点用户数</div>
          </div>
        </el-col>
      </el-row>
    </el-card>

    <!-- 返点记录 -->
    <el-card class="records-card" style="margin-top: 20px;">
      <template #header>
        <div class="card-header">
          <span>返点记录</span>
          <div>
            <el-button type="success" size="small" @click="handleDownload">
              导出记录
            </el-button>
          </div>
        </div>
      </template>
      
      <!-- 筛选条件 -->
      <div class="filter-container">
        <el-input
          v-model="listQuery.keyword"
          placeholder="请输入用户账号"
          style="width: 200px;"
          class="filter-item"
          @keyup.enter="handleFilter"
        />
        <el-select
          v-model="listQuery.level"
          placeholder="推荐级别"
          clearable
          style="width: 120px"
          class="filter-item"
        >
          <el-option label="一级" value="1" />
          <el-option label="二级" value="2" />
          <el-option label="三级" value="3" />
        </el-select>
        <el-select
          v-model="listQuery.status"
          placeholder="状态"
          clearable
          style="width: 120px"
          class="filter-item"
        >
          <el-option label="待结算" value="1" />
          <el-option label="已结算" value="2" />
          <el-option label="已取消" value="3" />
        </el-select>
        <el-button class="filter-item" type="primary" icon="Search" @click="handleFilter">
          搜索
        </el-button>
      </div>
      
      <el-table :data="rebateRecords" border style="width: 100%" v-loading="listLoading">
        <el-table-column prop="account" label="用户账号" width="120" />
        <el-table-column prop="referrerAccount" label="推荐人" width="120" />
        <el-table-column prop="level" label="推荐级别" width="100" align="center">
          <template #default="scope">
            <el-tag :type="getLevelType(scope.row.level)">
              {{ scope.row.level }}级
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="orderAmount" label="订单金额" width="120" align="right">
          <template #default="scope">
            ¥{{ scope.row.orderAmount }}
          </template>
        </el-table-column>
        <el-table-column prop="rebateRate" label="返点率" width="80" align="center">
          <template #default="scope">
            {{ scope.row.rebateRate }}%
          </template>
        </el-table-column>
        <el-table-column prop="rebateAmount" label="返点金额" width="120" align="right">
          <template #default="scope">
            ¥{{ scope.row.rebateAmount }}
          </template>
        </el-table-column>
        <el-table-column prop="status" label="状态" width="100" align="center">
          <template #default="scope">
            <el-tag :type="getStatusType(scope.row.status)">
              {{ getStatusText(scope.row.status) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="createTime" label="创建时间" width="160">
          <template #default="scope">
            {{ formatTime(scope.row.createTime) }}
          </template>
        </el-table-column>
        <el-table-column prop="settleTime" label="结算时间" width="160">
          <template #default="scope">
            <span v-if="scope.row.settleTime">{{ formatTime(scope.row.settleTime) }}</span>
            <span v-else class="text-muted">-</span>
          </template>
        </el-table-column>
        <el-table-column label="操作" align="center" width="120">
          <template #default="scope">
            <el-button
              v-if="scope.row.status === 1"
              type="primary"
              size="small"
              @click="handleSettle(scope.row)"
            >
              结算
            </el-button>
            <span v-else class="text-muted">-</span>
          </template>
        </el-table-column>
      </el-table>

      <pagination
        v-show="total > 0"
        :total="total"
        :page.sync="listQuery.page"
        :limit.sync="listQuery.limit"
        @pagination="getList"
      />
    </el-card>
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { getRebateRules, updateRebateRules, getRebateStats, getRebateRecords, settleRebate, exportRebateRecords } from '@/api/rebate'
import { parseTime } from '@/utils'
import Pagination from '@/components/Pagination/index.vue'

const ruleFormRef = ref()
const ruleLoading = ref(false)
const listLoading = ref(false)

const ruleForm = ref({
  level1Rate: 0,
  level2Rate: 0,
  level3Rate: 0,
  minAmount: 0,
  settlementCycle: 'realtime',
  status: true
})

const rebateStats = ref({
  todayRebate: 0,
  monthRebate: 0,
  totalRebate: 0,
  rebateUsers: 0
})

const rebateRecords = ref([])
const total = ref(0)
const listQuery = ref({
  page: 1,
  limit: 20,
  keyword: '',
  level: '',
  status: ''
})

const ruleRules = {
  level1Rate: [
    { required: true, message: '请输入一级推荐返点率', trigger: 'blur' },
    { type: 'number', min: 0, max: 100, message: '返点率范围0-100%', trigger: 'blur' }
  ],
  level2Rate: [
    { required: true, message: '请输入二级推荐返点率', trigger: 'blur' },
    { type: 'number', min: 0, max: 100, message: '返点率范围0-100%', trigger: 'blur' }
  ],
  level3Rate: [
    { required: true, message: '请输入三级推荐返点率', trigger: 'blur' },
    { type: 'number', min: 0, max: 100, message: '返点率范围0-100%', trigger: 'blur' }
  ],
  minAmount: [
    { required: true, message: '请输入最低返点金额', trigger: 'blur' },
    { type: 'number', min: 0, message: '金额不能小于0', trigger: 'blur' }
  ]
}

async function loadRebateRules() {
  try {
    const response = await getRebateRules()
    ruleForm.value = response.data || ruleForm.value
  } catch (error) {
    console.error('加载返点规则失败:', error)
    // 模拟数据
    ruleForm.value = {
      level1Rate: 5.0,
      level2Rate: 3.0,
      level3Rate: 1.0,
      minAmount: 10.0,
      settlementCycle: 'daily',
      status: true
    }
  }
}

async function saveRules() {
  if (!ruleFormRef.value) return
  
  try {
    await ruleFormRef.value.validate()
    ruleLoading.value = true
    
    await updateRebateRules(ruleForm.value)
    ElMessage.success('返点规则保存成功')
  } catch (error) {
    if (error !== false) {
      ElMessage.error('保存失败')
    }
  } finally {
    ruleLoading.value = false
  }
}

async function refreshStats() {
  try {
    const response = await getRebateStats()
    rebateStats.value = response.data
  } catch (error) {
    console.error('加载返点统计失败:', error)
    // 模拟数据
    rebateStats.value = {
      todayRebate: 256.80,
      monthRebate: 5678.90,
      totalRebate: 67890.12,
      rebateUsers: 456
    }
  }
}

async function getList() {
  listLoading.value = true
  try {
    const response = await getRebateRecords(listQuery.value)
    rebateRecords.value = response.data.list
    total.value = response.data.total
  } catch (error) {
    console.error('获取返点记录失败:', error)
    // 模拟数据
    rebateRecords.value = [
      {
        id: 1,
        account: 'user001',
        referrerAccount: 'referrer001',
        level: 1,
        orderAmount: 1000.00,
        rebateRate: 5.0,
        rebateAmount: 50.00,
        status: 2,
        createTime: new Date().getTime(),
        settleTime: new Date().getTime()
      },
      {
        id: 2,
        account: 'user002',
        referrerAccount: 'referrer001',
        level: 2,
        orderAmount: 500.00,
        rebateRate: 3.0,
        rebateAmount: 15.00,
        status: 1,
        createTime: new Date().getTime() - ********,
        settleTime: null
      }
    ]
    total.value = 2
  } finally {
    listLoading.value = false
  }
}

function handleFilter() {
  listQuery.value.page = 1
  getList()
}

async function handleSettle(row) {
  try {
    await ElMessageBox.confirm('确定要结算该返点记录吗？', '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    })

    await settleRebate(row.id)
    ElMessage.success('返点结算成功')
    getList()
    refreshStats()
  } catch (error) {
    if (error !== 'cancel') {
      ElMessage.error('结算失败')
    }
  }
}

async function handleDownload() {
  try {
    const response = await exportRebateRecords(listQuery.value)
    const blob = new Blob([response], { type: 'application/vnd.ms-excel' })
    const url = window.URL.createObjectURL(blob)
    const link = document.createElement('a')
    link.href = url
    link.download = `返点记录_${new Date().getTime()}.xlsx`
    link.click()
    window.URL.revokeObjectURL(url)
  } catch (error) {
    ElMessage.error('导出失败')
  }
}

function getLevelType(level) {
  const typeMap = {
    1: 'success',
    2: 'warning',
    3: 'info'
  }
  return typeMap[level] || 'info'
}

function getStatusType(status) {
  const statusMap = {
    1: 'warning',
    2: 'success',
    3: 'danger'
  }
  return statusMap[status] || 'info'
}

function getStatusText(status) {
  const statusMap = {
    1: '待结算',
    2: '已结算',
    3: '已取消'
  }
  return statusMap[status] || '未知'
}

function formatTime(time) {
  return parseTime(time, '{y}-{m}-{d} {h}:{i}:{s}')
}

onMounted(() => {
  loadRebateRules()
  refreshStats()
  getList()
})
</script>

<style scoped>
.rebate-header {
  margin-bottom: 20px;
}

.rebate-header h2 {
  margin: 0 0 8px 0;
  color: #303133;
}

.rebate-header p {
  margin: 0;
  color: #909399;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.unit {
  margin-left: 8px;
  color: #909399;
  font-size: 12px;
}

.stat-item {
  text-align: center;
  padding: 20px;
  border: 1px solid #ebeef5;
  border-radius: 4px;
}

.stat-value {
  font-size: 24px;
  font-weight: bold;
  color: #303133;
  margin-bottom: 8px;
}

.stat-label {
  font-size: 14px;
  color: #909399;
}

.filter-container {
  padding-bottom: 10px;
}

.filter-item {
  margin-right: 10px;
}

.text-muted {
  color: #999;
}
</style>
