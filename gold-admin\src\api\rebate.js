import request from '@/utils/request'

/**
 * 获取返点规则
 */
export function getRebateRules() {
  return request({
    url: '/admin/rebate/rules',
    method: 'get'
  })
}

/**
 * 更新返点规则
 * @param {object} data - 规则数据
 */
export function updateRebateRules(data) {
  return request({
    url: '/admin/rebate/rules',
    method: 'put',
    data
  })
}

/**
 * 获取返点统计
 */
export function getRebateStats() {
  return request({
    url: '/admin/rebate/stats',
    method: 'get'
  })
}

/**
 * 获取返点记录
 * @param {object} params - 查询参数
 */
export function getRebateRecords(params) {
  return request({
    url: '/admin/rebate/records',
    method: 'get',
    params
  })
}

/**
 * 结算返点
 * @param {number} id - 返点记录ID
 */
export function settleRebate(id) {
  return request({
    url: `/admin/rebate/${id}/settle`,
    method: 'put'
  })
}

/**
 * 导出返点记录
 * @param {object} params - 查询参数
 */
export function exportRebateRecords(params) {
  return request({
    url: '/admin/rebate/export',
    method: 'get',
    params,
    responseType: 'blob'
  })
}
