<template>
  <div class="app-container">
    <div class="fee-header">
      <h2>用户等级手续费管理</h2>
      <p>管理不同用户等级的手续费设置</p>
    </div>

    <!-- 用户等级手续费管理 -->
    <el-card class="platform-fee-card">
      <template #header>
        <div class="card-header">
          <span>用户等级手续费列表</span>
        </div>
      </template>

      <!-- 搜索筛选 -->
      <div class="filter-container" style="margin-bottom: 20px;">
        <el-input
          v-model="platformFeeQuery.keyword"
          placeholder="请输入用户等级或等级名称"
          style="width: 200px;"
          class="filter-item"
          @keyup.enter="handlePlatformFeeFilter"
        />
        <el-button
          class="filter-item"
          type="primary"
          icon="Search"
          @click="handlePlatformFeeFilter"
        >
          搜索
        </el-button>
        <el-button
          class="filter-item"
          icon="Refresh"
          @click="handlePlatformFeeReset"
        >
          重置
        </el-button>
      </div>

      <!-- 用户等级手续费表格 -->
      <el-table
        v-loading="platformFeeLoading"
        :data="filteredPlatformFees"
        border
        style="width: 100%"
      >
        <el-table-column prop="level" label="用户等级" width="120" align="center" />
        <el-table-column prop="levelName" label="等级名称" width="200" />
        <el-table-column prop="feeRate" label="手续费（元/克）" width="150" align="center">
          <template #default="scope">
            {{ formatFeeRate(scope.row.feeRate) }}
          </template>
        </el-table-column>
        <el-table-column label="操作" align="center" width="150">
          <template #default="scope">
            <el-button type="primary" size="small" @click="handleEditPlatformFee(scope.row)">
              编辑
            </el-button>
          </template>
        </el-table-column>
      </el-table>
    </el-card>

    <!-- 用户等级手续费设置对话框 -->
    <el-dialog
      title="编辑用户等级手续费"
      v-model="platformFeeDialogVisible"
      width="500px"
    >
      <el-form
        ref="platformFeeFormRef"
        :model="platformFeeForm"
        :rules="platformFeeRules"
        label-width="120px"
      >
        <el-form-item label="用户等级">
          <el-input-number
            v-model="platformFeeForm.userLevel"
            :min="1"
            :max="999"
            style="width: 100%"
            disabled
          />
        </el-form-item>
        <el-form-item label="等级名称">
          <el-input
            v-model="platformFeeForm.levelName"
            disabled
          />
        </el-form-item>
        <el-form-item label="手续费（元/克）" prop="feeRate">
          <el-input-number
            v-model="platformFeeForm.feeRate"
            :precision="2"
            :min="0"
            style="width: 100%"
            placeholder="请输入手续费"
          />
          <span class="unit">元/克</span>
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="platformFeeDialogVisible = false">
            取消
          </el-button>
          <el-button type="primary" @click="confirmPlatformFee" :loading="platformFeeSubmitLoading">
            确认
          </el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, onMounted, computed } from 'vue'
import { ElMessage } from 'element-plus'
import { getPlatformFees, setPlatformFee } from '@/api/fee'

// 用户等级手续费相关
const platformFeeLoading = ref(false)
const platformFees = ref([])
const platformFeeQuery = ref({
  keyword: ''
})
const platformFeeDialogVisible = ref(false)
const platformFeeSubmitLoading = ref(false)
const platformFeeForm = ref({
  userLevel: null,
  levelName: '',
  feeRate: null
})

// 过滤后的平台手续费数据
const filteredPlatformFees = computed(() => {
  if (!platformFeeQuery.value.keyword) {
    return platformFees.value
  }
  const keyword = platformFeeQuery.value.keyword.toLowerCase()
  return platformFees.value.filter(item =>
    item.level.toString().includes(keyword) ||
    item.levelName.toLowerCase().includes(keyword)
  )
})

const platformFeeRules = {
  feeRate: [
    { required: true, message: '请输入手续费', trigger: 'blur' },
    { type: 'number', min: 0, message: '手续费不能小于0', trigger: 'blur' }
  ]
}

// 用户等级手续费相关函数
async function loadPlatformFees() {
  platformFeeLoading.value = true
  try {
    const response = await getPlatformFees()
    console.log('API响应:', response)

    // 根据响应拦截器的处理，response就是后端返回的数据
    // 您提供的响应格式是直接返回数组
    if (Array.isArray(response)) {
      platformFees.value = response
    } else if (response && Array.isArray(response.data)) {
      platformFees.value = response.data
    } else {
      platformFees.value = []
      console.warn('响应数据格式不正确:', response)
    }

    console.log('获取用户等级手续费成功:', platformFees.value)
  } catch (error) {
    console.error('获取用户等级手续费失败:', error)
    ElMessage.error(`获取用户等级手续费失败: ${error.response?.data?.message || error.message || '请稍后重试'}`)

    // 开发环境下显示模拟数据，使用您提供的响应格式
    if (process.env.NODE_ENV === 'development') {
      platformFees.value = [
        {
          level: 2,
          levelName: "11",
          feeRate: 10.00
        },
        {
          level: 1,
          levelName: "1级客户",
          feeRate: 20.00
        }
      ]
    }
  } finally {
    platformFeeLoading.value = false
  }
}

function handleEditPlatformFee(row) {
  platformFeeForm.value = {
    userLevel: row.level,
    levelName: row.levelName,
    feeRate: row.feeRate // 直接使用原始值，不需要转换
  }
  platformFeeDialogVisible.value = true
}

async function confirmPlatformFee() {
  // 验证表单
  if (platformFeeForm.value.feeRate === null || platformFeeForm.value.feeRate < 0) {
    ElMessage.error('请输入有效的手续费')
    return
  }

  platformFeeSubmitLoading.value = true
  try {
    await setPlatformFee(
      platformFeeForm.value.userLevel,
      platformFeeForm.value.levelName,
      platformFeeForm.value.feeRate // 直接使用原始值
    )

    ElMessage.success('更新成功')
    platformFeeDialogVisible.value = false
    loadPlatformFees()
  } catch (error) {
    console.error('设置用户等级手续费失败:', error)
    ElMessage.error(`操作失败: ${error.response?.data?.message || error.message || '请稍后重试'}`)
  } finally {
    platformFeeSubmitLoading.value = false
  }
}

function handlePlatformFeeFilter() {
  // 过滤功能通过computed属性实现，这里不需要额外操作
}

function handlePlatformFeeReset() {
  platformFeeQuery.value.keyword = ''
}

function formatFeeRate(rate) {
  // 直接显示原始值，因为后端返回的就是元/克的值
  if (rate === null || rate === undefined) {
    return '0.00'
  }
  return Number(rate).toFixed(2)
}

onMounted(() => {
  loadPlatformFees()
})
</script>

<style scoped>
.fee-header {
  margin-bottom: 20px;
}

.fee-header h2 {
  margin: 0 0 8px 0;
  color: #303133;
}

.fee-header p {
  margin: 0;
  color: #909399;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.unit {
  margin-left: 8px;
  color: #909399;
  font-size: 12px;
}

.platform-fee-card {
  margin-top: 20px;
}

.filter-container {
  margin-bottom: 20px;
}

.filter-item {
  margin-right: 10px;
}

.dialog-footer {
  text-align: right;
}
</style>
